const db = require('./db');

/**
 * FUNGSI GENERATE KODE UNIK - BERBAGAI VERSI
 * 
 * Berdasarkan permintaan: "pastikan tidak ada kode unik yang sama"
 * Ada 2 interpretasi yang mungkin:
 * 1. Kode unik tidak boleh sama PER TANGGAL (maksimal 10 transaksi per hari)
 * 2. Kode unik tidak boleh sama SECARA GLOBAL (maksimal 10 transaksi total)
 */

// ========================================
// VERSI 1: KODE UNIK PER TANGGAL
// ========================================
// Kode unik 1-10 tidak boleh duplikasi dalam tanggal yang sama
// Memungkinkan lebih dari 10 transaksi total dengan menggunakan tanggal berbeda

function generateKodeUnikPerTanggal(tanggal) {
  return new Promise((resolve, reject) => {
    const sql = `SELECT kode_unik FROM orders WHERE tanggal = ?`;

    db.query(sql, [tanggal], (err, results) => {
      if (err) {
        console.error('❌ Error saat query kode unik:', err.message);
        return reject(err);
      }

      // Ambil semua kode unik yang sudah digunakan pada tanggal tersebut
      const existing = results.map(r => r.kode_unik);
      
      // Buat array kode unik yang tersedia (1-10)
      const kodeRange = Array.from({ length: 10 }, (_, i) => i + 1);
      
      // Filter kode yang belum digunakan pada tanggal ini
      const available = kodeRange.filter(k => !existing.includes(k));

      // Jika tidak ada kode yang tersedia untuk tanggal ini
      if (available.length === 0) {
        return reject(new Error(`Kode unik habis pada tanggal ${tanggal}. Maksimal 10 transaksi per hari.`));
      }

      // Pilih kode secara acak dari yang tersedia
      const randomKode = available[Math.floor(Math.random() * available.length)];
      
      console.log(`🔢 [Per Tanggal] Kode unik dipilih: ${randomKode} untuk tanggal ${tanggal}`);
      console.log(`   Tersedia: ${available.join(', ')} | Terpakai: ${existing.join(', ') || 'tidak ada'}`);
      
      resolve(randomKode);
    });
  });
}

// ========================================
// VERSI 2: KODE UNIK GLOBAL
// ========================================
// Kode unik 1-10 tidak boleh duplikasi di seluruh database
// Maksimal hanya 10 transaksi total

function generateKodeUnikGlobal() {
  return new Promise((resolve, reject) => {
    const sql = `SELECT DISTINCT kode_unik FROM orders ORDER BY kode_unik`;

    db.query(sql, (err, results) => {
      if (err) {
        console.error('❌ Error saat query kode unik:', err.message);
        return reject(err);
      }

      // Ambil semua kode unik yang sudah digunakan di seluruh database
      const existing = results.map(r => r.kode_unik);
      
      // Buat array kode unik yang tersedia (1-10)
      const kodeRange = Array.from({ length: 10 }, (_, i) => i + 1);
      
      // Filter kode yang belum digunakan secara global
      const available = kodeRange.filter(k => !existing.includes(k));

      // Jika tidak ada kode yang tersedia secara global
      if (available.length === 0) {
        return reject(new Error(`Semua kode unik (1-10) sudah digunakan secara global. Maksimal 10 transaksi total.`));
      }

      // Pilih kode secara acak dari yang tersedia
      const randomKode = available[Math.floor(Math.random() * available.length)];
      
      console.log(`🔢 [Global] Kode unik dipilih: ${randomKode}`);
      console.log(`   Tersedia: ${available.join(', ')} | Terpakai: ${existing.join(', ') || 'tidak ada'}`);
      
      resolve(randomKode);
    });
  });
}

// ========================================
// VERSI 3: KODE UNIK BERURUTAN
// ========================================
// Generate kode unik secara berurutan 1, 2, 3, dst

function generateKodeUnikBerurutan() {
  return new Promise((resolve, reject) => {
    const sql = `SELECT MAX(kode_unik) as max_kode FROM orders`;

    db.query(sql, (err, results) => {
      if (err) {
        console.error('❌ Error saat query kode unik:', err.message);
        return reject(err);
      }

      const maxKode = results[0].max_kode || 0;
      const nextKode = maxKode + 1;

      // Jika sudah mencapai batas maksimal
      if (nextKode > 10) {
        return reject(new Error(`Kode unik maksimal (10) sudah tercapai. Tidak bisa membuat transaksi baru.`));
      }

      console.log(`🔢 [Berurutan] Kode unik dipilih: ${nextKode} (setelah ${maxKode})`);
      
      resolve(nextKode);
    });
  });
}

// ========================================
// FUNGSI UTILITAS
// ========================================

// Fungsi untuk cek kode unik yang tersedia (per tanggal)
function cekKodeUnikTersediaPerTanggal(tanggal) {
  return new Promise((resolve, reject) => {
    const sql = `SELECT kode_unik FROM orders WHERE tanggal = ? ORDER BY kode_unik`;

    db.query(sql, [tanggal], (err, results) => {
      if (err) return reject(err);

      const existing = results.map(r => r.kode_unik);
      const kodeRange = Array.from({ length: 10 }, (_, i) => i + 1);
      const available = kodeRange.filter(k => !existing.includes(k));

      resolve({
        tanggal,
        terpakai: existing,
        tersedia: available,
        jumlah_terpakai: existing.length,
        jumlah_tersedia: available.length
      });
    });
  });
}

// Fungsi untuk cek kode unik yang tersedia (global)
function cekKodeUnikTersediaGlobal() {
  return new Promise((resolve, reject) => {
    const sql = `SELECT DISTINCT kode_unik FROM orders ORDER BY kode_unik`;

    db.query(sql, (err, results) => {
      if (err) return reject(err);

      const existing = results.map(r => r.kode_unik);
      const kodeRange = Array.from({ length: 10 }, (_, i) => i + 1);
      const available = kodeRange.filter(k => !existing.includes(k));

      resolve({
        terpakai: existing,
        tersedia: available,
        jumlah_terpakai: existing.length,
        jumlah_tersedia: available.length
      });
    });
  });
}

// Fungsi untuk menampilkan status kode unik
async function tampilkanStatusKodeUnik() {
  try {
    console.log('\n📊 STATUS KODE UNIK:');
    console.log('─'.repeat(40));
    
    // Status global
    const globalStatus = await cekKodeUnikTersediaGlobal();
    console.log('🌍 GLOBAL:');
    console.log(`   Terpakai: ${globalStatus.terpakai.join(', ') || 'tidak ada'} (${globalStatus.jumlah_terpakai}/10)`);
    console.log(`   Tersedia: ${globalStatus.tersedia.join(', ') || 'tidak ada'} (${globalStatus.jumlah_tersedia}/10)`);
    
    // Status per tanggal hari ini
    const today = new Date().toISOString().split('T')[0];
    const todayStatus = await cekKodeUnikTersediaPerTanggal(today);
    console.log(`\n📅 HARI INI (${today}):`);
    console.log(`   Terpakai: ${todayStatus.terpakai.join(', ') || 'tidak ada'} (${todayStatus.jumlah_terpakai}/10)`);
    console.log(`   Tersedia: ${todayStatus.tersedia.join(', ') || 'tidak ada'} (${todayStatus.jumlah_tersedia}/10)`);
    
  } catch (error) {
    console.error('❌ Error saat cek status:', error.message);
  }
}

// Export semua fungsi
module.exports = {
  generateKodeUnikPerTanggal,
  generateKodeUnikGlobal,
  generateKodeUnikBerurutan,
  cekKodeUnikTersediaPerTanggal,
  cekKodeUnikTersediaGlobal,
  tampilkanStatusKodeUnik
};

// Jika file ini dijalankan langsung, tampilkan status
if (require.main === module) {
  tampilkanStatusKodeUnik().then(() => {
    process.exit();
  });
}
