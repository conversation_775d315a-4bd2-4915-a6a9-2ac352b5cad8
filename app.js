const db = require('./db');

// Konstanta sesuai permintaan
const HARGA = 299000; // 299 ribu untuk semua produk
const STATUS = 'menunggu';

// Fungsi untuk mendapatkan tanggal hari ini
function getRandomTanggal() {
  const today = new Date();
  return today.toISOString().split('T')[0]; // format YYYY-MM-DD
}

// Fungsi untuk generate nama produk yang bervariasi
function generateNamaProduk() {
  const produkList = [
    'Produk A - Premium',
    'Produk B - Standard',
    'Produk C - Deluxe',
    'Produk D - Basic',
    'Produk E - Pro'
  ];
  return produkList[Math.floor(Math.random() * produkList.length)];
}

// Fungsi utama untuk membuat 50 transaksi
async function insertTransaksi50() {
  const tanggal = getRandomTanggal();
  const transaksiTotal = 50;
  let berhasil = 0;
  let gagal = 0;

  console.log(`🚀 Memulai pembuatan ${transaksiTotal} transaksi untuk tanggal: ${tanggal}`);
  console.log('📝 Ketentuan:');
  console.log('   - Harga: Rp 299.000 untuk semua produk');
  console.log('   - Kode unik: 1-10 (maksimal 10 transaksi per hari)');
  console.log('   - Status: menunggu');
  console.log('');

  for (let i = 0; i < transaksiTotal; i++) {
    try {
      const kode_unik = await generateKodeUnik(tanggal);
      const produk_id = Math.floor(Math.random() * 1000) + 1; // acak id produk 1-1000
      const nama_produk = generateNamaProduk(); // gunakan nama produk yang bervariasi

      await insertOrder({
        produk_id,
        nama_produk,
        harga: HARGA,
        kode_unik,
        status: STATUS,
        tanggal,
      });

      berhasil++;
      console.log(`✅ Transaksi ${i + 1}/${transaksiTotal} disimpan (ID Produk: ${produk_id}, Kode Unik: ${kode_unik})`);

    } catch (error) {
      gagal++;
      console.error(`❌ Gagal simpan transaksi ke-${i + 1}:`, error.message);

      // Jika kode unik habis, hentikan proses
      if (error.message.includes('Kode unik habis')) {
        console.log(`⚠️  Kode unik habis! Hanya bisa membuat ${berhasil} transaksi untuk tanggal ${tanggal}`);
        break;
      }
    }
  }

  console.log('\n📊 Ringkasan:');
  console.log(`   ✅ Berhasil: ${berhasil} transaksi`);
  console.log(`   ❌ Gagal: ${gagal} transaksi`);
  console.log(`   📅 Tanggal: ${tanggal}`);

  // Tampilkan data yang berhasil disimpan
  await tampilkanDataTersimpan(tanggal);

  process.exit();
}

function insertOrder(order) {
  return new Promise((resolve, reject) => {
    const sql = `
      INSERT INTO orders (produk_id, nama_produk, harga, kode_unik, status, tanggal)
      VALUES (?, ?, ?, ?, ?, ?)
    `;

    db.query(sql, [
      order.produk_id,
      order.nama_produk,
      order.harga,
      order.kode_unik,
      order.status,
      order.tanggal
    ], (err, result) => {
      if (err) return reject(err);
      resolve(result);
    });
  });
}

// Fungsi untuk generate kode unik (1-10) yang belum digunakan pada tanggal tertentu
function generateKodeUnik(tanggal) {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT kode_unik FROM orders WHERE tanggal = ?
    `;

    db.query(sql, [tanggal], (err, results) => {
      if (err) {
        console.error('❌ Error saat query kode unik:', err.message);
        return reject(err);
      }

      // Ambil semua kode unik yang sudah digunakan pada tanggal tersebut
      const existing = results.map(r => r.kode_unik);

      // Buat array kode unik yang tersedia (1-10)
      const kodeRange = Array.from({ length: 10 }, (_, i) => i + 1);

      // Filter kode yang belum digunakan
      const available = kodeRange.filter(k => !existing.includes(k));

      // Jika tidak ada kode yang tersedia
      if (available.length === 0) {
        return reject(new Error(`Kode unik habis pada tanggal ${tanggal}. Maksimal 10 transaksi per hari.`));
      }

      // Pilih kode secara acak dari yang tersedia
      const randomKode = available[Math.floor(Math.random() * available.length)];

      console.log(`🔢 Kode unik dipilih: ${randomKode} (tersedia: ${available.join(', ')})`);
      resolve(randomKode);
    });
  });
}

// Fungsi untuk menampilkan data yang tersimpan
function tampilkanDataTersimpan(tanggal) {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT id, produk_id, nama_produk, harga, kode_unik, status, tanggal
      FROM orders
      WHERE tanggal = ?
      ORDER BY kode_unik ASC
    `;

    db.query(sql, [tanggal], (err, results) => {
      if (err) return reject(err);

      console.log('\n📋 Data transaksi yang tersimpan:');
      console.log('ID\tProduk ID\tNama Produk\t\tHarga\t\tKode Unik\tStatus');
      console.log('─'.repeat(80));

      results.forEach(row => {
        console.log(`${row.id}\t${row.produk_id}\t\t${row.nama_produk.substring(0,15)}...\tRp ${row.harga.toLocaleString()}\t${row.kode_unik}\t\t${row.status}`);
      });

      console.log(`\n📊 Total: ${results.length} transaksi`);
      resolve(results);
    });
  });
}

insertTransaksi50();
