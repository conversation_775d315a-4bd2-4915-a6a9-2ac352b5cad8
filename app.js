const db = require('./db');

// Konstanta sesuai permintaan
const HARGA = 299000; // 299 ribu untuk semua produk
const STATUS = 'menunggu';

// Fungsi untuk mendapatkan tanggal random dalam rentang 30 hari terakhir
function getRandomTanggal() {
  const today = new Date();
  const daysBack = Math.floor(Math.random() * 30); // 0-29 hari ke belakang
  const randomDate = new Date(today);
  randomDate.setDate(today.getDate() - daysBack);
  return randomDate.toISOString().split('T')[0]; // format YYYY-MM-DD
}

// Fungsi untuk mendapatkan tanggal hari ini
function getTodayDate() {
  const today = new Date();
  return today.toISOString().split('T')[0];
}

// Fungsi untuk generate nama produk yang bervariasi
function generateNamaProduk() {
  const produkList = [
    'Produk A - Premium',
    'Produk B - Standard',
    'Produk C - Deluxe',
    'Produk D - Basic',
    'Produk E - Pro'
  ];
  return produkList[Math.floor(Math.random() * produkList.length)];
}

// Fungsi utama untuk membuat setidaknya 50 transaksi
async function insertTransaksi50() {
  const transaksiTotal = 50;
  let berhasil = 0;
  let gagal = 0;
  const tanggalDigunakan = new Set();

  console.log(`🚀 Memulai pembuatan ${transaksiTotal} transaksi`);
  console.log('📝 Ketentuan:');
  console.log('   - Harga: Rp 299.000 untuk semua produk');
  console.log('   - Kode unik: 1-10 per tanggal');
  console.log('   - Status: menunggu');
  console.log('   - Menggunakan tanggal berbeda untuk mencapai 50 transaksi');
  console.log('');

  for (let i = 0; i < transaksiTotal; i++) {
    try {
      // Gunakan tanggal random untuk setiap transaksi
      const tanggal = getRandomTanggal();
      const kode_unik = await generateKodeUnik(tanggal);
      const produk_id = Math.floor(Math.random() * 1000) + 1; // acak id produk 1-1000
      const nama_produk = generateNamaProduk(); // gunakan nama produk yang bervariasi

      await insertOrder({
        produk_id,
        nama_produk,
        harga: HARGA,
        kode_unik,
        status: STATUS,
        tanggal,
      });

      berhasil++;
      tanggalDigunakan.add(tanggal);
      console.log(`✅ Transaksi ${i + 1}/${transaksiTotal} disimpan (Tanggal: ${tanggal}, ID Produk: ${produk_id}, Kode Unik: ${kode_unik})`);

    } catch (error) {
      gagal++;
      console.error(`❌ Gagal simpan transaksi ke-${i + 1}:`, error.message);

      // Jika ada error, coba lagi dengan tanggal yang berbeda
      if (error.message.includes('Kode unik habis')) {
        console.log(`⚠️  Kode unik habis untuk tanggal tersebut, mencoba tanggal lain...`);
        i--; // ulangi iterasi ini
        continue;
      }
    }
  }

  console.log('\n📊 Ringkasan Akhir:');
  console.log(`   ✅ Berhasil: ${berhasil} transaksi`);
  console.log(`   ❌ Gagal: ${gagal} transaksi`);
  console.log(`   📅 Tanggal yang digunakan: ${tanggalDigunakan.size} hari berbeda`);
  console.log(`   📅 Daftar tanggal: ${Array.from(tanggalDigunakan).sort().join(', ')}`);

  // Tampilkan ringkasan data per tanggal
  await tampilkanRingkasanData();

  process.exit();
}

function insertOrder(order) {
  return new Promise((resolve, reject) => {
    const sql = `
      INSERT INTO orders (produk_id, nama_produk, harga, kode_unik, status, tanggal)
      VALUES (?, ?, ?, ?, ?, ?)
    `;

    db.query(sql, [
      order.produk_id,
      order.nama_produk,
      order.harga,
      order.kode_unik,
      order.status,
      order.tanggal
    ], (err, result) => {
      if (err) return reject(err);
      resolve(result);
    });
  });
}

// Fungsi untuk generate kode unik (1-10) yang belum digunakan pada tanggal tertentu
function generateKodeUnik(tanggal) {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT kode_unik FROM orders WHERE tanggal = ?
    `;

    db.query(sql, [tanggal], (err, results) => {
      if (err) {
        console.error('❌ Error saat query kode unik:', err.message);
        return reject(err);
      }

      // Ambil semua kode unik yang sudah digunakan pada tanggal tersebut
      const existing = results.map(r => r.kode_unik);

      // Buat array kode unik yang tersedia (1-10)
      const kodeRange = Array.from({ length: 10 }, (_, i) => i + 1);

      // Filter kode yang belum digunakan
      const available = kodeRange.filter(k => !existing.includes(k));

      // Jika tidak ada kode yang tersedia
      if (available.length === 0) {
        return reject(new Error(`Kode unik habis pada tanggal ${tanggal}. Maksimal 10 transaksi per hari.`));
      }

      // Pilih kode secara acak dari yang tersedia
      const randomKode = available[Math.floor(Math.random() * available.length)];

      console.log(`🔢 Kode unik dipilih: ${randomKode} (tersedia: ${available.join(', ')})`);
      resolve(randomKode);
    });
  });
}

// Fungsi untuk menampilkan ringkasan data semua transaksi
function tampilkanRingkasanData() {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT
        tanggal,
        COUNT(*) as jumlah_transaksi,
        GROUP_CONCAT(kode_unik ORDER BY kode_unik) as kode_unik_digunakan
      FROM orders
      GROUP BY tanggal
      ORDER BY tanggal DESC
    `;

    db.query(sql, (err, results) => {
      if (err) return reject(err);

      console.log('\n📋 Ringkasan Data Transaksi:');
      console.log('Tanggal\t\tJumlah\tKode Unik yang Digunakan');
      console.log('─'.repeat(60));

      let totalTransaksi = 0;
      results.forEach(row => {
        totalTransaksi += row.jumlah_transaksi;
        console.log(`${row.tanggal}\t${row.jumlah_transaksi}\t${row.kode_unik_digunakan}`);
      });

      console.log('─'.repeat(60));
      console.log(`📊 TOTAL SEMUA TRANSAKSI: ${totalTransaksi}`);
      console.log(`📅 Jumlah hari berbeda: ${results.length}`);

      resolve(results);
    });
  });
}

// Fungsi untuk menampilkan data yang tersimpan untuk tanggal tertentu
function tampilkanDataTersimpan(tanggal) {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT id, produk_id, nama_produk, harga, kode_unik, status, tanggal
      FROM orders
      WHERE tanggal = ?
      ORDER BY kode_unik ASC
    `;

    db.query(sql, [tanggal], (err, results) => {
      if (err) return reject(err);

      console.log(`\n📋 Data transaksi untuk tanggal ${tanggal}:`);
      console.log('ID\tProduk ID\tNama Produk\t\tHarga\t\tKode Unik\tStatus');
      console.log('─'.repeat(80));

      results.forEach(row => {
        console.log(`${row.id}\t${row.produk_id}\t\t${row.nama_produk.substring(0,15)}...\tRp ${row.harga.toLocaleString()}\t${row.kode_unik}\t\t${row.status}`);
      });

      console.log(`\n📊 Total: ${results.length} transaksi`);
      resolve(results);
    });
  });
}

insertTransaksi50();
