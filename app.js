const db = require('./db');

const NAMA_PRODUK = 'Produk A';
const HARGA = 299000;
const STATUS = 'menunggu';

function getRandomTanggal() {
  const today = new Date();
  return today.toISOString().split('T')[0]; // format YYYY-MM-DD
}

async function insertTransaksi50() {
  const tanggal = getRandomTanggal();
  const transaksiTotal = 50;

  for (let i = 0; i < transaksiTotal; i++) {
    try {
      const kode_unik = await generateKodeUnik(tanggal);
      const produk_id = Math.floor(Math.random() * 1000); // acak id produk

      await insertOrder({
        produk_id,
        nama_produk: NAMA_PRODUK,
        harga: HARGA,
        kode_unik,
        status: STATUS,
        tanggal,
      });

      console.log(`✅ Transaksi ${i + 1} disimpan (kode_unik: ${kode_unik})`);

    } catch (error) {
      console.error(`❌ Gagal simpan transaksi ke-${i + 1}:`, error.message);
    }
  }

  process.exit();
}

function insertOrder(order) {
  return new Promise((resolve, reject) => {
    const sql = `
      INSERT INTO orders (produk_id, nama_produk, harga, kode_unik, status, tanggal)
      VALUES (?, ?, ?, ?, ?, ?)
    `;

    db.query(sql, [
      order.produk_id,
      order.nama_produk,
      order.harga,
      order.kode_unik,
      order.status,
      order.tanggal
    ], (err, result) => {
      if (err) return reject(err);
      resolve(result);
    });
  });
}

function generateKodeUnik(tanggal) {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT kode_unik FROM orders WHERE tanggal = ?
    `;

    db.query(sql, [tanggal], (err, results) => {
      if (err) return reject(err);

      const existing = results.map(r => r.kode_unik);
      const kodeRange = Array.from({ length: 10 }, (_, i) => i + 1);
      const available = kodeRange.filter(k => !existing.includes(k));

      if (available.length === 0) {
        return reject(new Error(`Kode unik habis pada tanggal ${tanggal}`));
      }

      const randomKode = available[Math.floor(Math.random() * available.length)];
      resolve(randomKode);
    });
  });
}

insertTransaksi50();
