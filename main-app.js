
const db = require('./db');
const HARGA_PRODUK = 299000; // 299 ribu untuk semua produk
const STATUS_DEFAULT = 'pending';


/**
 * // FUNGSI GENERATE KODE UNIK
 * @param {string} tanggal - Tanggal dalam format YYYY-MM-DD
 * @returns {Promise<number>} - Kode unik yang belum digunakan
 */
function generateKodeUnik(tanggal) {
  return new Promise((resolve, reject) => {
    // Query untuk mengambil kode unik yang sudah digunakan pada tanggal tertentu
    const sql = `
      SELECT kode_unik 
      FROM orders 
      WHERE tanggal = ?
      ORDER BY kode_unik
    `;

    db.query(sql, [tanggal], (err, results) => {
      if (err) {
        console.error('❌ Error saat query database:', err.message);
        return reject(err);
      }

      // Ambil semua kode unik yang sudah digunakan
      const kodeUnikTerpakai = results.map(row => row.kode_unik);
      
      // Buat array kode unik yang tersedia (1 sampai 10)
      const kodeUnikTersedia = [];
      for (let i = 1; i <= 10; i++) {
        if (!kodeUnikTerpakai.includes(i)) {
          kodeUnikTersedia.push(i);
        }
      }

      // Jika tidak ada kode unik yang tersedia
      if (kodeUnikTersedia.length === 0) {
        return reject(new Error(`Tidak ada kode unik yang tersedia untuk tanggal ${tanggal}. Semua kode 1-10 sudah digunakan.`));
      }

      // Pilih kode unik secara random dari yang tersedia
      const randomIndex = Math.floor(Math.random() * kodeUnikTersedia.length);
      const kodeUnikTerpilih = kodeUnikTersedia[randomIndex];

      console.log(`🔢 Kode unik terpilih: ${kodeUnikTerpilih} untuk tanggal ${tanggal}`);
      console.log(`   Kode tersedia: [${kodeUnikTersedia.join(', ')}]`);
      console.log(`   Kode terpakai: [${kodeUnikTerpakai.join(', ') || 'tidak ada'}]`);

      resolve(kodeUnikTerpilih);
    });
  });
}

// FUNGSI INSERT ORDER
/**
 * Fungsi untuk insert data order ke database
 * 
 * @param {Object} orderData - Data order yang akan disimpan
 * @returns {Promise<Object>} - Result dari insert
 */
function insertOrder(orderData) {
  return new Promise((resolve, reject) => {
    const sql = `
      INSERT INTO orders (produk_id, nama_produk, harga, kode_unik, status, tanggal)
      VALUES (?, ?, ?, ?, ?, ?)
    `;

    const values = [
      orderData.produk_id,
      orderData.nama_produk,
      orderData.harga,
      orderData.kode_unik,
      orderData.status,
      orderData.tanggal
    ];

    db.query(sql, values, (err, result) => {
      if (err) {
        console.error('❌ Error saat insert order:', err.message);
        return reject(err);
      }

      console.log(`✅ Order berhasil disimpan dengan ID: ${result.insertId}`);
      resolve(result);
    });
  });
}

// FUNGSI UTILITAS
// Generate nama produk random
function generateNamaProduk() {
  const daftarProduk = [
    'Smartphone Premium',
    'Laptop Gaming',
    'Headphone Wireless',
    'Smart Watch',
    'Tablet Android',
    'Camera Digital',
    'Speaker Bluetooth',
    'Power Bank'
  ];
  
  const randomIndex = Math.floor(Math.random() * daftarProduk.length);
  return daftarProduk[randomIndex];
}

// Generate produk ID random
function generateProdukId() {
  return Math.floor(Math.random() * 9000) + 1000; // 1000-9999
}

// Generate tanggal dalam rentang tertentu
function generateTanggal(hariKebelakang = 30) {
  const today = new Date();
  const randomDays = Math.floor(Math.random() * hariKebelakang);
  const targetDate = new Date(today);
  targetDate.setDate(today.getDate() - randomDays);
  return targetDate.toISOString().split('T')[0]; // Format YYYY-MM-DD
}

// FUNGSI UTAMA - BUAT 50 TRANSAKSI
async function buatSetidaknya50Transaksi() {
  const TARGET_MINIMAL = 50;
  let jumlahBerhasil = 0;
  let jumlahGagal = 0;
  let percobaan = 0;
  const MAX_PERCOBAAN = 200; // Batas maksimal percobaan

  console.log('🚀 MEMULAI PEMBUATAN TRANSAKSI');
  console.log('═'.repeat(50));
  console.log(`📋 Target: Minimal ${TARGET_MINIMAL} transaksi`);
  console.log('📋 Ketentuan:');
  console.log('   a. Harga produk: Rp 299.000 untuk semua produk');
  console.log('   b. Kode unik: 1 sampai 10');
  console.log('   c. Tidak ada kode unik yang sama (per tanggal)');
  console.log('   d. Menggunakan NodeJS + MySQL');
  console.log('');

  // Loop untuk membuat transaksi
  while (jumlahBerhasil < TARGET_MINIMAL && percobaan < MAX_PERCOBAAN) {
    percobaan++;
    
    try {
      // Generate data transaksi
      const tanggal = generateTanggal(60); 
      const produkId = generateProdukId();
      const namaProduk = generateNamaProduk();
      
      // Generate kode unik untuk tanggal tersebut
      const kodeUnik = await generateKodeUnik(tanggal);
      
      // Data order lengkap
      const orderData = {
        produk_id: produkId,
        nama_produk: namaProduk,
        harga: HARGA_PRODUK,
        kode_unik: kodeUnik,
        status: STATUS_DEFAULT,
        tanggal: tanggal
      };

      // Insert ke database
      await insertOrder(orderData);
      
      jumlahBerhasil++;
      console.log(`📦 Transaksi ${jumlahBerhasil}/${TARGET_MINIMAL} - ${namaProduk} (${tanggal})`);
      
    } catch (error) {
      jumlahGagal++;
      
      // Log error setiap 10 percobaan gagal
      if (jumlahGagal % 10 === 0) {
        console.log(`⚠️  Percobaan ${percobaan}: ${jumlahBerhasil} berhasil, ${jumlahGagal} gagal`);
      }
      
      // Jika error karena kode unik habis, lanjut ke tanggal lain
      if (error.message.includes('Tidak ada kode unik yang tersedia')) {
        continue; // Coba tanggal lain
      } else {
        console.error(`❌ Error tidak terduga:`, error.message);
      }
    }
  }

  // Tampilkan hasil akhir
  console.log('\n🎉 SELESAI!');
  console.log('═'.repeat(50));
  console.log(`✅ Transaksi berhasil: ${jumlahBerhasil}`);
  console.log(`❌ Transaksi gagal: ${jumlahGagal}`);
  console.log(`🔄 Total percobaan: ${percobaan}`);
  
  if (jumlahBerhasil >= TARGET_MINIMAL) {
    console.log(`🎯 TARGET TERCAPAI! Berhasil membuat ${jumlahBerhasil} transaksi (target: ${TARGET_MINIMAL})`);
  } else {
    console.log(`⚠️  Target belum tercapai. Hanya ${jumlahBerhasil} dari ${TARGET_MINIMAL} transaksi.`);
  }

  // Tampilkan statistik dari database
  await tampilkanStatistikDatabase();
  
  process.exit();
}

// ========================================
// FUNGSI STATISTIK
// ========================================
function tampilkanStatistikDatabase() {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT 
        COUNT(*) as total_transaksi,
        COUNT(DISTINCT tanggal) as total_hari,
        COUNT(DISTINCT kode_unik) as total_kode_unik,
        SUM(harga) as total_nilai,
        AVG(harga) as rata_rata_harga,
        MIN(tanggal) as tanggal_pertama,
        MAX(tanggal) as tanggal_terakhir
      FROM orders
    `;

    db.query(sql, (err, results) => {
      if (err) return reject(err);

      const stats = results[0];
      
      console.log('\n📊 STATISTIK DATABASE:');
      console.log('─'.repeat(40));
      console.log(`📈 Total transaksi: ${stats.total_transaksi}`);
      console.log(`📅 Total hari berbeda: ${stats.total_hari}`);
      console.log(`🔢 Total kode unik digunakan: ${stats.total_kode_unik}/10`);
      console.log(`💰 Total nilai: Rp ${parseInt(stats.total_nilai).toLocaleString()}`);
      console.log(`💰 Rata-rata harga: Rp ${parseInt(stats.rata_rata_harga).toLocaleString()}`);
      console.log(`📅 Periode: ${stats.tanggal_pertama} s/d ${stats.tanggal_terakhir}`);
      
      resolve(stats);
    });
  });
}

// ========================================
// JALANKAN APLIKASI
// ========================================
if (require.main === module) {
  console.log('🎯 APLIKASI ORDER MANAGEMENT');
  console.log('Membuat setidaknya 50 transaksi dengan kode unik 1-10');
  console.log('');
  
  buatSetidaknya50Transaksi();
}

// Export untuk digunakan di file lain
module.exports = {
  generateKodeUnik,
  insertOrder,
  buatSetidaknya50Transaksi,
  generateNamaProduk,
  generateProdukId,
  generateTanggal,
  tampilkanStatistikDatabase,
  HARGA_PRODUK,
  STATUS_DEFAULT
};
