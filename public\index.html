<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            margin: 20px auto;
            max-width: 1200px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px 20px 0 0;
            padding: 30px;
            text-align: center;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .btn-custom {
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
        }
        
        .btn-primary-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
        }
        
        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success-custom {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
            color: white;
        }
        
        .btn-danger-custom {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            border: none;
            color: white;
        }
        
        .table-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .log-container {
            background: #1a1a1a;
            color: #00ff00;
            border-radius: 10px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px;
        }
        
        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-success {
            background: #d1edff;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="header">
                <h1><i class="fas fa-shopping-cart"></i> Order Management System</h1>
                <p class="mb-0">Generate transaksi dengan kode unik 1-10 | Harga: Rp 299.000</p>
            </div>
            
            <!-- Control Panel -->
            <div class="row p-4">
                <div class="col-md-12">
                    <div class="d-flex flex-wrap justify-content-center gap-3 mb-4">
                        <button class="btn btn-custom btn-primary-custom" onclick="generateTransaksi()">
                            <i class="fas fa-plus"></i> Generate 50 Transaksi
                        </button>
                        <button class="btn btn-custom btn-success-custom" onclick="loadData()">
                            <i class="fas fa-refresh"></i> Refresh Data
                        </button>
                        <button class="btn btn-custom btn-danger-custom" onclick="resetData()">
                            <i class="fas fa-trash"></i> Reset Data
                        </button>
                        <button class="btn btn-custom btn-primary-custom" onclick="checkKodeUnik()">
                            <i class="fas fa-search"></i> Cek Kode Unik
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Statistics Cards -->
            <div class="row px-4">
                <div class="col-md-3">
                    <div class="stats-card text-center">
                        <i class="fas fa-shopping-bag fa-2x text-primary mb-2"></i>
                        <h3 id="totalTransaksi" class="text-primary">0</h3>
                        <p class="text-muted mb-0">Total Transaksi</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card text-center">
                        <i class="fas fa-calendar fa-2x text-success mb-2"></i>
                        <h3 id="totalHari" class="text-success">0</h3>
                        <p class="text-muted mb-0">Hari Berbeda</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card text-center">
                        <i class="fas fa-key fa-2x text-warning mb-2"></i>
                        <h3 id="totalKodeUnik" class="text-warning">0/10</h3>
                        <p class="text-muted mb-0">Kode Unik Digunakan</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card text-center">
                        <i class="fas fa-money-bill fa-2x text-info mb-2"></i>
                        <h3 id="totalNilai" class="text-info">Rp 0</h3>
                        <p class="text-muted mb-0">Total Nilai</p>
                    </div>
                </div>
            </div>
            
            <!-- Loading -->
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p>Memproses...</p>
            </div>
            
            <!-- Log Container -->
            <div id="logContainer" class="log-container" style="display: none;">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <strong>Log Output:</strong>
                    <button class="btn btn-sm btn-outline-light" onclick="clearLog()">Clear</button>
                </div>
                <div id="logOutput"></div>
            </div>
            
            <!-- Data Table -->
            <div class="table-container">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4><i class="fas fa-table"></i> Data Transaksi</h4>
                    <div>
                        <input type="date" id="filterDate" class="form-control d-inline-block" style="width: auto;" onchange="filterByDate()">
                        <button class="btn btn-outline-secondary btn-sm ms-2" onclick="clearFilter()">Clear Filter</button>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>ID</th>
                                <th>Produk ID</th>
                                <th>Nama Produk</th>
                                <th>Harga</th>
                                <th>Kode Unik</th>
                                <th>Status</th>
                                <th>Tanggal</th>
                                <th>Created At</th>
                            </tr>
                        </thead>
                        <tbody id="dataTable">
                            <tr>
                                <td colspan="8" class="text-center text-muted">
                                    <i class="fas fa-inbox fa-2x mb-2"></i><br>
                                    Tidak ada data. Klik "Refresh Data" untuk memuat data.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
