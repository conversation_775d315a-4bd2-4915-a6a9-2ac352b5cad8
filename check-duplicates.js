const db = require('./db');

async function checkDuplicates() {
  try {
    console.log('🔍 MENGECEK DUPLIKASI KODE UNIK');
    console.log('═'.repeat(50));
    
    // Cek duplikasi dalam tanggal yang sama
    const duplicatesQuery = `
      SELECT tanggal, kode_unik, COUNT(*) as jumlah
      FROM orders 
      GROUP BY tanggal, kode_unik 
      HAVING COUNT(*) > 1 
      ORDER BY tanggal, kode_unik
    `;
    
    const duplicates = await new Promise((resolve, reject) => {
      db.query(duplicatesQuery, (err, results) => {
        if (err) reject(err);
        else resolve(results);
      });
    });
    
    if (duplicates.length > 0) {
      console.log('❌ DITEMUKAN DUPLIKASI KODE UNIK DALAM TANGGAL YANG SAMA:');
      console.log('Tanggal\t\tKode Unik\tJumlah');
      console.log('─'.repeat(40));
      duplicates.forEach(row => {
        console.log(`${row.tanggal}\t${row.kode_unik}\t\t${row.jumlah}`);
      });
    } else {
      console.log('✅ TIDAK ADA DUPLIKASI dalam tanggal yang sama');
    }
    
    // Cek penggunaan kode unik secara global
    console.log('\n📊 PENGGUNAAN KODE UNIK SECARA GLOBAL:');
    const globalUsageQuery = `
      SELECT kode_unik, COUNT(*) as total_penggunaan
      FROM orders 
      GROUP BY kode_unik 
      ORDER BY kode_unik
    `;
    
    const globalUsage = await new Promise((resolve, reject) => {
      db.query(globalUsageQuery, (err, results) => {
        if (err) reject(err);
        else resolve(results);
      });
    });
    
    console.log('Kode Unik\tTotal Penggunaan');
    console.log('─'.repeat(30));
    globalUsage.forEach(row => {
      console.log(`${row.kode_unik}\t\t${row.total_penggunaan}`);
    });
    
    // Cek apakah ada kode di luar range 1-10
    console.log('\n🔍 CEK KODE UNIK DI LUAR RANGE 1-10:');
    const outOfRangeQuery = `
      SELECT kode_unik, COUNT(*) as jumlah
      FROM orders 
      WHERE kode_unik < 1 OR kode_unik > 10
      GROUP BY kode_unik
      ORDER BY kode_unik
    `;
    
    const outOfRange = await new Promise((resolve, reject) => {
      db.query(outOfRangeQuery, (err, results) => {
        if (err) reject(err);
        else resolve(results);
      });
    });
    
    if (outOfRange.length > 0) {
      console.log('❌ DITEMUKAN KODE UNIK DI LUAR RANGE 1-10:');
      console.log('Kode Unik\tJumlah');
      console.log('─'.repeat(20));
      outOfRange.forEach(row => {
        console.log(`${row.kode_unik}\t\t${row.jumlah}`);
      });
    } else {
      console.log('✅ SEMUA KODE UNIK dalam range 1-10');
    }
    
    // Tampilkan contoh data per tanggal
    console.log('\n📋 CONTOH DATA PER TANGGAL (5 tanggal terakhir):');
    const sampleQuery = `
      SELECT tanggal, GROUP_CONCAT(kode_unik ORDER BY kode_unik) as kode_unik_list, COUNT(*) as total
      FROM orders 
      GROUP BY tanggal 
      ORDER BY tanggal DESC 
      LIMIT 5
    `;
    
    const sampleData = await new Promise((resolve, reject) => {
      db.query(sampleQuery, (err, results) => {
        if (err) reject(err);
        else resolve(results);
      });
    });
    
    console.log('Tanggal\t\tKode Unik yang Digunakan\t\tTotal');
    console.log('─'.repeat(60));
    sampleData.forEach(row => {
      console.log(`${row.tanggal}\t${row.kode_unik_list}\t\t\t${row.total}`);
    });
    
    // Summary
    console.log('\n📊 SUMMARY:');
    const totalQuery = `SELECT COUNT(*) as total FROM orders`;
    const total = await new Promise((resolve, reject) => {
      db.query(totalQuery, (err, results) => {
        if (err) reject(err);
        else resolve(results[0].total);
      });
    });
    
    console.log(`Total transaksi: ${total}`);
    console.log(`Duplikasi dalam tanggal sama: ${duplicates.length > 0 ? 'ADA ❌' : 'TIDAK ADA ✅'}`);
    console.log(`Kode di luar range 1-10: ${outOfRange.length > 0 ? 'ADA ❌' : 'TIDAK ADA ✅'}`);
    
    if (duplicates.length > 0 || outOfRange.length > 0) {
      console.log('\n⚠️  MASALAH DITEMUKAN! Fungsi generate kode unik perlu diperbaiki.');
    } else {
      console.log('\n✅ FUNGSI GENERATE KODE UNIK BEKERJA DENGAN BENAR!');
      console.log('   - Tidak ada duplikasi dalam tanggal yang sama');
      console.log('   - Semua kode dalam range 1-10');
      console.log('   - Kode yang sama boleh digunakan di tanggal berbeda');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    db.end();
  }
}

if (require.main === module) {
  checkDuplicates();
}

module.exports = { checkDuplicates };
