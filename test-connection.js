// Script untuk test koneksi database
const db = require('./db');

async function testConnection() {
  try {
    console.log('🔄 Testing koneksi database...');
    
    // Test koneksi dengan query sederhana
    const result = await new Promise((resolve, reject) => {
      db.query('SELECT 1 as test', (err, results) => {
        if (err) reject(err);
        else resolve(results);
      });
    });
    
    console.log('✅ Koneksi database berhasil!');
    console.log('📊 Test query result:', result);
    
    // Test apakah tabel orders ada
    const tableCheck = await new Promise((resolve, reject) => {
      db.query('SHOW TABLES LIKE "orders"', (err, results) => {
        if (err) reject(err);
        else resolve(results);
      });
    });
    
    if (tableCheck.length > 0) {
      console.log('✅ Tabel orders ditemukan!');
      
      // Cek struktur tabel
      const tableStructure = await new Promise((resolve, reject) => {
        db.query('DESCRIBE orders', (err, results) => {
          if (err) reject(err);
          else resolve(results);
        });
      });
      
      console.log('📋 Struktur tabel orders:');
      tableStructure.forEach(field => {
        console.log(`   ${field.Field}: ${field.Type} ${field.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${field.Key ? field.Key : ''}`);
      });
      
      // Cek jumlah data yang ada
      const countData = await new Promise((resolve, reject) => {
        db.query('SELECT COUNT(*) as total FROM orders', (err, results) => {
          if (err) reject(err);
          else resolve(results[0].total);
        });
      });
      
      console.log(`📊 Total data di tabel orders: ${countData} record`);
      
    } else {
      console.log('⚠️  Tabel orders belum ada. Jalankan: npm run setup');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('💡 Tips: Periksa username dan password MySQL di file db.js');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('💡 Tips: Pastikan MySQL server sudah berjalan');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('💡 Tips: Database "test" belum ada. Jalankan: npm run setup');
    }
  } finally {
    db.end();
  }
}

// Jalankan test jika file ini dijalankan langsung
if (require.main === module) {
  testConnection();
}
