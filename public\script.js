// API Base URL
const API_BASE = '/api';

// Global variables
let allData = [];
let filteredData = [];

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadData();
    loadStats();
});

// Show/Hide loading
function showLoading() {
    document.getElementById('loading').style.display = 'block';
}

function hideLoading() {
    document.getElementById('loading').style.display = 'none';
}

// Show/Hide log
function showLog() {
    document.getElementById('logContainer').style.display = 'block';
}

function hideLog() {
    document.getElementById('logContainer').style.display = 'none';
}

// Add log message
function addLog(message, type = 'info') {
    const logOutput = document.getElementById('logOutput');
    const timestamp = new Date().toLocaleTimeString();
    const color = type === 'error' ? '#ff6b6b' : type === 'success' ? '#51cf66' : '#00ff00';
    
    logOutput.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
    logOutput.scrollTop = logOutput.scrollHeight;
}

// Clear log
function clearLog() {
    document.getElementById('logOutput').innerHTML = '';
}

// Generate 50 Transaksi
async function generateTransaksi() {
    showLoading();
    showLog();
    clearLog();
    
    try {
        addLog('🚀 Memulai generate 50 transaksi...', 'info');
        
        const response = await fetch(`${API_BASE}/generate-transaksi`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (result.success) {
            addLog(`✅ Berhasil generate ${result.data.berhasil} transaksi`, 'success');
            addLog(`📊 Total percobaan: ${result.data.percobaan}`, 'info');
            addLog(`📅 Tanggal digunakan: ${result.data.tanggal_digunakan} hari`, 'info');
            
            // Refresh data and stats
            await loadData();
            await loadStats();
            
            // Show success alert
            showAlert('success', `Berhasil generate ${result.data.berhasil} transaksi!`);
        } else {
            addLog(`❌ Error: ${result.message}`, 'error');
            showAlert('danger', result.message);
        }
        
    } catch (error) {
        console.error('Error:', error);
        addLog(`❌ Error: ${error.message}`, 'error');
        showAlert('danger', 'Terjadi kesalahan saat generate transaksi');
    } finally {
        hideLoading();
    }
}

// Load data from API
async function loadData() {
    try {
        const response = await fetch(`${API_BASE}/orders`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (result.success) {
            allData = result.data;
            filteredData = [...allData];
            renderTable();
        } else {
            showAlert('danger', result.message);
        }
        
    } catch (error) {
        console.error('Error loading data:', error);
        showAlert('danger', 'Gagal memuat data');
    }
}

// Load statistics
async function loadStats() {
    try {
        const response = await fetch(`${API_BASE}/stats`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (result.success) {
            const stats = result.data;
            document.getElementById('totalTransaksi').textContent = stats.total_transaksi || 0;
            document.getElementById('totalHari').textContent = stats.total_hari || 0;
            document.getElementById('totalKodeUnik').textContent = `${stats.total_kode_unik || 0}/10`;
            document.getElementById('totalNilai').textContent = `Rp ${(stats.total_nilai || 0).toLocaleString()}`;
        }
        
    } catch (error) {
        console.error('Error loading stats:', error);
    }
}

// Reset data
async function resetData() {
    if (!confirm('Apakah Anda yakin ingin menghapus SEMUA data transaksi?')) {
        return;
    }
    
    showLoading();
    showLog();
    clearLog();
    
    try {
        addLog('⚠️ Menghapus semua data...', 'info');
        
        const response = await fetch(`${API_BASE}/reset`, {
            method: 'DELETE'
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (result.success) {
            addLog(`✅ Berhasil menghapus ${result.data.deleted_count} record`, 'success');
            
            // Refresh data and stats
            await loadData();
            await loadStats();
            
            showAlert('success', 'Data berhasil dihapus!');
        } else {
            addLog(`❌ Error: ${result.message}`, 'error');
            showAlert('danger', result.message);
        }
        
    } catch (error) {
        console.error('Error:', error);
        addLog(`❌ Error: ${error.message}`, 'error');
        showAlert('danger', 'Terjadi kesalahan saat reset data');
    } finally {
        hideLoading();
    }
}

// Check kode unik status
async function checkKodeUnik() {
    showLog();
    clearLog();
    
    try {
        addLog('🔍 Mengecek status kode unik...', 'info');
        
        const response = await fetch(`${API_BASE}/kode-unik-status`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (result.success) {
            const global = result.data.global;
            const today = result.data.today;
            
            addLog('🌍 STATUS GLOBAL:', 'info');
            addLog(`   Terpakai: [${global.terpakai.join(', ') || 'tidak ada'}] (${global.jumlah_terpakai}/10)`, 'info');
            addLog(`   Tersedia: [${global.tersedia.join(', ') || 'tidak ada'}] (${global.jumlah_tersedia}/10)`, 'info');
            
            addLog(`📅 STATUS HARI INI (${today.tanggal}):`, 'info');
            addLog(`   Terpakai: [${today.terpakai.join(', ') || 'tidak ada'}] (${today.jumlah_terpakai}/10)`, 'info');
            addLog(`   Tersedia: [${today.tersedia.join(', ') || 'tidak ada'}] (${today.jumlah_tersedia}/10)`, 'info');
        } else {
            addLog(`❌ Error: ${result.message}`, 'error');
        }
        
    } catch (error) {
        console.error('Error:', error);
        addLog(`❌ Error: ${error.message}`, 'error');
    }
}

// Render table
function renderTable() {
    const tbody = document.getElementById('dataTable');
    
    if (filteredData.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center text-muted">
                    <i class="fas fa-inbox fa-2x mb-2"></i><br>
                    Tidak ada data yang sesuai filter.
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = filteredData.map(row => `
        <tr>
            <td>${row.id}</td>
            <td>${row.produk_id}</td>
            <td>${row.nama_produk}</td>
            <td>Rp ${parseInt(row.harga).toLocaleString()}</td>
            <td><span class="badge bg-primary">${row.kode_unik}</span></td>
            <td><span class="status-badge status-${row.status === 'pending' ? 'pending' : 'success'}">${row.status}</span></td>
            <td>${formatDate(row.tanggal)}</td>
            <td>${formatDateTime(row.created_at)}</td>
        </tr>
    `).join('');
}

// Filter by date
function filterByDate() {
    const filterDate = document.getElementById('filterDate').value;
    
    if (!filterDate) {
        filteredData = [...allData];
    } else {
        filteredData = allData.filter(row => row.tanggal === filterDate);
    }
    
    renderTable();
}

// Clear filter
function clearFilter() {
    document.getElementById('filterDate').value = '';
    filteredData = [...allData];
    renderTable();
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID');
}

// Format datetime
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('id-ID');
}

// Show alert
function showAlert(type, message) {
    // Remove existing alerts
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => alert.remove());
    
    // Create new alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
