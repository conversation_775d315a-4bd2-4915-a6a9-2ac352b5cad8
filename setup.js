const mysql = require('mysql2');
const fs = require('fs');
const path = require('path');

// Konfigurasi database
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '',     // sesuaikan dengan password MySQL Anda
  multipleStatements: true  // untuk menjalankan multiple SQL statements
};

async function setupDatabase() {
  let connection;
  
  try {
    console.log('🔄 Memulai setup database...');
    
    // Buat koneksi tanpa database terlebih dahulu
    connection = mysql.createConnection(dbConfig);
    
    // Baca file SQL
    const sqlFile = path.join(__dirname, 'create_table.sql');
    const sqlContent = fs.readFileSync(sqlFile, 'utf8');
    
    console.log('📄 Membaca file SQL...');
    
    // Jalankan SQL script
    await new Promise((resolve, reject) => {
      connection.query(sqlContent, (err, results) => {
        if (err) {
          reject(err);
        } else {
          resolve(results);
        }
      });
    });
    
    console.log('✅ Database dan tabel berhasil dibuat!');
    console.log('📋 Struktur tabel orders:');
    console.log('   - id (AUTO_INCREMENT PRIMARY KEY)');
    console.log('   - produk_id (INT)');
    console.log('   - nama_produk (VARCHAR)');
    console.log('   - harga (DECIMAL)');
    console.log('   - kode_unik (INT, 1-10)');
    console.log('   - status (VARCHAR)');
    console.log('   - tanggal (DATE)');
    console.log('   - created_at, updated_at (TIMESTAMP)');
    console.log('');
    console.log('🎯 Constraint yang diterapkan:');
    console.log('   - kode_unik harus unik per tanggal');
    console.log('   - kode_unik hanya boleh 1-10');
    console.log('');
    console.log('🚀 Setup selesai! Anda bisa menjalankan: npm start');
    
  } catch (error) {
    console.error('❌ Error saat setup database:', error.message);
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('💡 Tips: Periksa username dan password MySQL di file setup.js');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('💡 Tips: Pastikan MySQL server sudah berjalan');
    }
    
    process.exit(1);
  } finally {
    if (connection) {
      connection.end();
    }
  }
}

// Jalankan setup jika file ini dijalankan langsung
if (require.main === module) {
  setupDatabase();
}

module.exports = { setupDatabase };
