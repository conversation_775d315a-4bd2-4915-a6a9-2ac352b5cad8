# Project Order Management - Soal 2

Aplikasi NodeJS untuk generate transaksi order dengan sistem kode unik yang tidak duplikat.

## 📋 Spesifikasi

- **Tabel**: `orders` dengan field: id, produk_id, nama_produk, harga, kode_unik, status, tanggal
- **Harga**: Rp 299.000 untuk semua produk
- **Kode Unik**: 1-10 (maksimal 10 transaksi per hari)
- **Transaksi**: Minimal 50 transaksi
- **Database**: MySQL

## 🚀 Cara Menjalankan

### 1. Persiapan Database

Pastikan MySQL sudah terinstall dan ber<PERSON>lan, lalu:

```bash
# Setup database dan tabel
npm run setup
```

Atau jalankan manual file SQL:
```sql
mysql -u root -p < create_table.sql
```

### 2. Konfigurasi Database

Edit file `db.js` dan sesuaikan konfigurasi:
```javascript
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '',     // isi password MySQL Anda
  database: 'test'
};
```

### 3. Jalankan Aplikasi

```bash
# Jalankan aplikasi untuk generate 50 transaksi
npm start

# Atau gunakan script khusus
npm run generate-50
```

**Catatan**: Karena kode unik hanya 1-10 per tanggal, aplikasi akan menggunakan tanggal yang berbeda-beda dalam rentang 30 hari terakhir untuk mencapai target 50 transaksi.

## 📁 Struktur File

```
soal-2/
├── generate-50-transaksi.js  # Script utama untuk generate 50 transaksi
├── app.js                    # File aplikasi original
├── db.js                     # Konfigurasi koneksi database
├── setup.js                  # Script setup database
├── create_table.sql          # SQL untuk membuat tabel
├── test-connection.js        # Script test koneksi database
├── view-data.js              # Script untuk melihat data
├── reset-data.js             # Script untuk reset/hapus data
├── package.json              # Konfigurasi npm
└── README.md                 # Dokumentasi ini
```

## 🔧 Fitur Utama

### 1. Generate Kode Unik
- Kode unik 1-10 per tanggal
- Tidak ada duplikasi kode pada tanggal yang sama
- Otomatis berhenti jika kode habis

### 2. Insert Transaksi
- Generate 50 transaksi otomatis
- Harga tetap Rp 299.000
- Status default "menunggu"
- Produk ID random 1-1000

### 3. Validasi Database
- Constraint unique untuk kode_unik per tanggal
- Check constraint untuk kode_unik 1-10
- Index untuk performa query

## 📊 Contoh Output

```
🚀 Memulai pembuatan 50 transaksi untuk tanggal: 2025-07-31
📝 Ketentuan:
   - Harga: Rp 299.000 untuk semua produk
   - Kode unik: 1-10 (maksimal 10 transaksi per hari)
   - Status: menunggu

✅ Transaksi 1/50 disimpan (ID Produk: 456, Kode Unik: 3)
✅ Transaksi 2/50 disimpan (ID Produk: 789, Kode Unik: 7)
...
⚠️  Kode unik habis! Hanya bisa membuat 10 transaksi untuk tanggal 2025-07-31

📊 Ringkasan:
   ✅ Berhasil: 10 transaksi
   ❌ Gagal: 0 transaksi
   📅 Tanggal: 2025-07-31
```

## 🛠 Script NPM

- `npm run setup` - Setup database dan tabel
- `npm start` - Generate 50 transaksi (script utama)
- `npm run generate-50` - Generate 50 transaksi (alias)
- `npm run view-data` - Lihat semua data transaksi
- `npm run test-connection` - Test koneksi database
- `npm run reset-data` - Hapus semua data (hati-hati!)
- `npm run dev` - Jalankan versi development
- `npm run create-table` - Buat tabel (alias untuk setup)

## ⚠️ Catatan Penting

1. **Kode Unik Terbatas**: Maksimal 10 transaksi per hari karena kode unik hanya 1-10
2. **Database**: Pastikan database "test" sudah ada atau akan dibuat otomatis
3. **Koneksi**: Sesuaikan username/password MySQL di file `db.js`

## 🔍 Troubleshooting

### Error: ER_ACCESS_DENIED_ERROR
- Periksa username dan password MySQL di `db.js`

### Error: ECONNREFUSED
- Pastikan MySQL server sudah berjalan

### Error: ER_BAD_DB_ERROR
- Jalankan `npm run setup` untuk membuat database

### Kode Unik Habis
- Normal jika sudah ada 10 transaksi pada tanggal yang sama
- Ganti tanggal atau hapus data lama untuk testing

## 📝 Struktur Tabel Orders

```sql
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    produk_id INT NOT NULL,
    nama_produk VARCHAR(255) NOT NULL,
    harga DECIMAL(10,2) NOT NULL,
    kode_unik INT NOT NULL,
    status VARCHAR(50) NOT NULL,
    tanggal DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_kode_per_tanggal (tanggal, kode_unik),
    CHECK (kode_unik >= 1 AND kode_unik <= 10)
);
```
