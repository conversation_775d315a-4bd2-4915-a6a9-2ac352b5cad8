const db = require('./db');

// Konstanta sesuai permintaan
const HARGA = 299000; // 299 ribu untuk semua produk
const STATUS = 'menunggu';

// Fungsi untuk generate nama produk yang bervariasi
function generateNamaProduk() {
  const produkList = [
    'Produk A - Premium',
    'Produk B - Standard', 
    'Produk C - Deluxe',
    'Produk D - Basic',
    'Produk E - Pro',
    'Produk F - Elite',
    'Produk G - Classic',
    'Produk H - Modern'
  ];
  return produkList[Math.floor(Math.random() * produkList.length)];
}

// Fungsi untuk mendapatkan tanggal hari ini
function getTodayDate() {
  const today = new Date();
  return today.toISOString().split('T')[0];
}

// Fungsi untuk generate tanggal dalam rentang tertentu
function generateTanggalRange(startDaysBack = 10, endDaysBack = 0) {
  const today = new Date();
  const daysBack = Math.floor(Math.random() * (startDaysBack - endDaysBack + 1)) + endDaysBack;
  const randomDate = new Date(today);
  randomDate.setDate(today.getDate() - daysBack);
  return randomDate.toISOString().split('T')[0];
}

// Fungsi untuk generate kode unik GLOBAL (tidak ada duplikasi sama sekali)
function generateKodeUnikGlobal() {
  return new Promise((resolve, reject) => {
    const sql = `SELECT DISTINCT kode_unik FROM orders`;

    db.query(sql, (err, results) => {
      if (err) {
        console.error('❌ Error saat query kode unik:', err.message);
        return reject(err);
      }

      // Ambil semua kode unik yang sudah digunakan di seluruh database
      const existing = results.map(r => r.kode_unik);
      
      // Buat array kode unik yang tersedia (1-10)
      const kodeRange = Array.from({ length: 10 }, (_, i) => i + 1);
      
      // Filter kode yang belum digunakan
      const available = kodeRange.filter(k => !existing.includes(k));

      // Jika tidak ada kode yang tersedia
      if (available.length === 0) {
        return reject(new Error(`Semua kode unik (1-10) sudah digunakan. Maksimal 10 transaksi total.`));
      }

      // Pilih kode secara acak dari yang tersedia
      const randomKode = available[Math.floor(Math.random() * available.length)];
      
      console.log(`🔢 Kode unik dipilih: ${randomKode} (tersedia: ${available.join(', ')})`);
      resolve(randomKode);
    });
  });
}

// Fungsi untuk insert order ke database
function insertOrder(order) {
  return new Promise((resolve, reject) => {
    const sql = `
      INSERT INTO orders (produk_id, nama_produk, harga, kode_unik, status, tanggal)
      VALUES (?, ?, ?, ?, ?, ?)
    `;

    db.query(sql, [
      order.produk_id,
      order.nama_produk,
      order.harga,
      order.kode_unik,
      order.status,
      order.tanggal
    ], (err, result) => {
      if (err) return reject(err);
      resolve(result);
    });
  });
}

// Fungsi utama untuk membuat transaksi dengan kode unik global
async function generate50TransaksiGlobalUnique() {
  const TARGET_TRANSAKSI = 50;
  let berhasil = 0;
  let gagal = 0;
  const tanggalDigunakan = new Map();
  let attempts = 0;
  const MAX_ATTEMPTS = 100;

  console.log(`🚀 Target: Membuat ${TARGET_TRANSAKSI} transaksi`);
  console.log('📝 Ketentuan:');
  console.log('   - Harga: Rp 299.000 untuk semua produk');
  console.log('   - Kode unik: 1-10 (GLOBAL UNIQUE - tidak ada duplikasi sama sekali)');
  console.log('   - Status: menunggu');
  console.log('   - Maksimal hanya 10 transaksi total karena kode unik terbatas');
  console.log('');

  while (berhasil < TARGET_TRANSAKSI && attempts < MAX_ATTEMPTS) {
    attempts++;
    
    try {
      // Generate tanggal random
      const tanggal = generateTanggalRange(30, 0);
      
      const kode_unik = await generateKodeUnikGlobal();
      const produk_id = Math.floor(Math.random() * 1000) + 1;
      const nama_produk = generateNamaProduk();

      await insertOrder({
        produk_id,
        nama_produk,
        harga: HARGA,
        kode_unik,
        status: STATUS,
        tanggal,
      });

      berhasil++;
      
      // Update tracking tanggal
      if (!tanggalDigunakan.has(tanggal)) {
        tanggalDigunakan.set(tanggal, 0);
      }
      tanggalDigunakan.set(tanggal, tanggalDigunakan.get(tanggal) + 1);
      
      console.log(`✅ Transaksi ${berhasil}/${TARGET_TRANSAKSI} - Tanggal: ${tanggal}, Produk: ${produk_id}, Kode: ${kode_unik}`);

    } catch (error) {
      gagal++;
      console.error(`❌ Error transaksi ke-${attempts}:`, error.message);
      
      // Jika kode unik habis, hentikan
      if (error.message.includes('Semua kode unik')) {
        console.log(`⚠️  Kode unik habis! Maksimal hanya ${berhasil} transaksi bisa dibuat.`);
        break;
      }
    }
  }

  console.log('\n🎉 SELESAI! Ringkasan Akhir:');
  console.log(`   ✅ Berhasil: ${berhasil} transaksi`);
  console.log(`   ❌ Gagal: ${gagal} transaksi`);
  console.log(`   🔄 Total percobaan: ${attempts}`);
  console.log(`   📅 Tanggal yang digunakan: ${tanggalDigunakan.size} hari berbeda`);
  
  if (berhasil < TARGET_TRANSAKSI) {
    console.log(`\n⚠️  CATATAN: Target ${TARGET_TRANSAKSI} transaksi tidak tercapai karena keterbatasan kode unik (1-10).`);
    console.log(`   Dengan kode unik global unique, maksimal hanya 10 transaksi yang bisa dibuat.`);
  }
  
  console.log('\n📊 Detail per tanggal:');
  const sortedDates = Array.from(tanggalDigunakan.entries()).sort((a, b) => a[0].localeCompare(b[0]));
  sortedDates.forEach(([tanggal, count]) => {
    console.log(`   ${tanggal}: ${count} transaksi`);
  });
  
  // Tampilkan ringkasan total dari database
  await tampilkanStatistikFinal();
  
  process.exit();
}

// Fungsi untuk menampilkan statistik final dari database
function tampilkanStatistikFinal() {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT 
        COUNT(*) as total_transaksi,
        COUNT(DISTINCT tanggal) as total_hari,
        COUNT(DISTINCT kode_unik) as total_kode_unik,
        MIN(tanggal) as tanggal_pertama,
        MAX(tanggal) as tanggal_terakhir,
        SUM(harga) as total_nilai,
        GROUP_CONCAT(DISTINCT kode_unik ORDER BY kode_unik) as kode_unik_digunakan
      FROM orders
    `;

    db.query(sql, (err, results) => {
      if (err) return reject(err);

      const stats = results[0];
      
      console.log('\n📈 STATISTIK FINAL DARI DATABASE:');
      console.log('─'.repeat(50));
      console.log(`📊 Total Transaksi: ${stats.total_transaksi}`);
      console.log(`📅 Total Hari Berbeda: ${stats.total_hari}`);
      console.log(`🔢 Total Kode Unik Digunakan: ${stats.total_kode_unik}`);
      console.log(`🔢 Kode Unik yang Digunakan: ${stats.kode_unik_digunakan}`);
      console.log(`📅 Periode: ${stats.tanggal_pertama} s/d ${stats.tanggal_terakhir}`);
      console.log(`💰 Total Nilai: Rp ${stats.total_nilai.toLocaleString()}`);
      console.log(`💰 Rata-rata per Transaksi: Rp ${(stats.total_nilai / stats.total_transaksi).toLocaleString()}`);
      
      resolve(stats);
    });
  });
}

// Jalankan jika file ini dijalankan langsung
if (require.main === module) {
  generate50TransaksiGlobalUnique();
}

module.exports = { generate50TransaksiGlobalUnique };
