// Script untuk reset/hapus semua data orders
const db = require('./db');

async function resetData() {
  try {
    console.log('⚠️  PERINGATAN: Script ini akan menghapus SEMUA data di tabel orders!');
    console.log('🔄 Memulai reset data...');
    
    // Hapus semua data
    const result = await new Promise((resolve, reject) => {
      db.query('DELETE FROM orders', (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });
    
    console.log(`✅ Berhasil menghapus ${result.affectedRows} record`);
    
    // Reset auto increment
    await new Promise((resolve, reject) => {
      db.query('ALTER TABLE orders AUTO_INCREMENT = 1', (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });
    
    console.log('✅ Auto increment direset ke 1');
    
    // Verifikasi tabel kosong
    const count = await new Promise((resolve, reject) => {
      db.query('SELECT COUNT(*) as total FROM orders', (err, results) => {
        if (err) reject(err);
        else resolve(results[0].total);
      });
    });
    
    console.log(`📊 Verifikasi: Tabel orders sekarang memiliki ${count} record`);
    console.log('🎯 Tabel orders siap untuk data baru!');
    
  } catch (error) {
    console.error('❌ Error saat reset data:', error.message);
  } finally {
    db.end();
  }
}

// Fungsi untuk hapus data berdasarkan tanggal
async function resetDataByDate(tanggal) {
  try {
    console.log(`⚠️  Menghapus data untuk tanggal: ${tanggal}`);
    
    const result = await new Promise((resolve, reject) => {
      db.query('DELETE FROM orders WHERE tanggal = ?', [tanggal], (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });
    
    console.log(`✅ Berhasil menghapus ${result.affectedRows} record untuk tanggal ${tanggal}`);
    
  } catch (error) {
    console.error('❌ Error saat hapus data:', error.message);
  } finally {
    db.end();
  }
}

// Jalankan berdasarkan argument
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length > 0) {
    // Jika ada argument tanggal, hapus data untuk tanggal tersebut
    const tanggal = args[0];
    resetDataByDate(tanggal);
  } else {
    // Jika tidak ada argument, hapus semua data
    resetData();
  }
}

module.exports = { resetData, resetDataByDate };
