{"name": "soal-2", "version": "1.0.0", "description": "Aplikasi NodeJS untuk generate transaksi order dengan kode unik", "main": "app.js", "scripts": {"start": "node server.js", "server": "node server.js", "ui": "node server.js", "setup": "node setup.js", "main": "node main-app.js", "demo": "node demo-kode-unik.js", "create-table": "node setup.js", "test-connection": "node test-connection.js", "view-data": "node view-data.js", "check-kode-unik": "node kode-unik-functions.js", "generate-50": "node generate-50-transaksi.js", "reset-data": "node reset-data.js", "dev": "node app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["nodejs", "mysql", "order", "transaction", "unique-code"], "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "mysql2": "^3.14.3"}, "engines": {"node": ">=14.0.0"}}