# 🔢 DOKUMENTASI FUNGSI GENERATE KODE UNIK

## 📋 Ketentuan yang Dipenuhi

Sesuai dengan permintaan:
- ✅ **Harga produk**: 299 ribu untuk semua produk
- ✅ **Kode unik**: 1 sampai 10
- ✅ **Tidak ada kode unik yang sama** (per tanggal)
- ✅ **50 transaksi**: Berhasil dibuat
- ✅ **NodeJS + MySQL**: Lengkap dengan koneksi database

## 🔧 Fungsi Generate Kode Unik

### 📍 Lokasi Fungsi
File: `main-app.js` (baris 32-67)

### 🎯 Cara <PERSON>rja

```javascript
function generateKodeUnik(tanggal) {
  return new Promise((resolve, reject) => {
    // 1. Query kode unik yang sudah digunakan pada tanggal tertentu
    const sql = `
      SELECT kode_unik 
      FROM orders 
      WHERE tanggal = ?
      ORDER BY kode_unik
    `;

    db.query(sql, [tanggal], (err, results) => {
      if (err) {
        return reject(err);
      }

      // 2. Ambil semua kode unik yang sudah digunakan
      const kodeUnikTerpakai = results.map(row => row.kode_unik);
      
      // 3. Buat array kode unik yang tersedia (1 sampai 10)
      const kodeUnikTersedia = [];
      for (let i = 1; i <= 10; i++) {
        if (!kodeUnikTerpakai.includes(i)) {
          kodeUnikTersedia.push(i);
        }
      }

      // 4. Validasi ketersediaan
      if (kodeUnikTersedia.length === 0) {
        return reject(new Error(`Tidak ada kode unik yang tersedia untuk tanggal ${tanggal}`));
      }

      // 5. Pilih kode unik secara random
      const randomIndex = Math.floor(Math.random() * kodeUnikTersedia.length);
      const kodeUnikTerpilih = kodeUnikTersedia[randomIndex];

      resolve(kodeUnikTerpilih);
    });
  });
}
```

## 🎯 Fitur Utama

### 1. **Validasi Kode Unik Per Tanggal**
- Memastikan tidak ada duplikasi kode unik dalam tanggal yang sama
- Maksimal 10 transaksi per hari (kode 1-10)
- Memungkinkan kode yang sama digunakan di tanggal berbeda

### 2. **Random Selection**
- Memilih kode unik secara acak dari yang tersedia
- Tidak berurutan, sehingga lebih natural

### 3. **Error Handling**
- Menangani kasus ketika semua kode unik sudah terpakai
- Memberikan pesan error yang informatif

### 4. **Logging Detail**
- Menampilkan kode yang terpilih
- Menampilkan kode yang tersedia dan terpakai
- Memudahkan debugging dan monitoring

## 📊 Contoh Output Fungsi

```
🔢 Kode unik terpilih: 7 untuk tanggal 2025-08-01
   Kode tersedia: [2, 4, 6, 7, 8, 9]
   Kode terpakai: [1, 3, 5, 10]
```

## 🧪 Hasil Testing

### ✅ Test 1: Tanggal Kosong
- **Input**: Tanggal baru (2025-08-01)
- **Output**: Kode 1-10 semua tersedia
- **Hasil**: ✅ Berhasil memilih kode random

### ✅ Test 2: Tanggal dengan Data
- **Input**: Tanggal yang sudah ada transaksi
- **Output**: Hanya kode yang belum digunakan
- **Hasil**: ✅ Tidak ada duplikasi

### ✅ Test 3: Tanggal Penuh
- **Input**: Tanggal dengan 10 transaksi
- **Output**: Error "tidak ada kode unik yang tersedia"
- **Hasil**: ✅ Validasi bekerja

### ✅ Test 4: Multiple Tanggal
- **Input**: Berbagai tanggal berbeda
- **Output**: Kode yang sama bisa digunakan di tanggal berbeda
- **Hasil**: ✅ Sistem per tanggal bekerja

## 📈 Statistik Hasil

Dari eksekusi terakhir:
- **Total transaksi**: 55 (50 + 5 demo)
- **Total hari berbeda**: 33 hari
- **Penggunaan kode unik**:
  - Kode 1: 9 kali digunakan
  - Kode 2: 3 kali digunakan
  - Kode 3: 7 kali digunakan
  - Kode 4: 4 kali digunakan
  - Kode 5: 5 kali digunakan
  - Kode 6: 2 kali digunakan
  - Kode 7: 5 kali digunakan
  - Kode 8: 8 kali digunakan
  - Kode 9: 4 kali digunakan
  - Kode 10: 8 kali digunakan

## 🔍 Verifikasi Tidak Ada Duplikasi

### Query Verifikasi:
```sql
-- Cek duplikasi kode unik per tanggal
SELECT tanggal, kode_unik, COUNT(*) as jumlah
FROM orders 
GROUP BY tanggal, kode_unik 
HAVING COUNT(*) > 1;
```

**Hasil**: Tidak ada duplikasi! ✅

## 🚀 Cara Menggunakan

### 1. Setup Database
```bash
npm run setup
```

### 2. Jalankan Aplikasi Utama
```bash
npm start  # Membuat 50 transaksi
```

### 3. Demo Fungsi Kode Unik
```bash
npm run demo  # Demo cara kerja fungsi
```

### 4. Lihat Data
```bash
npm run view-data  # Lihat semua data
```

### 5. Cek Status Kode Unik
```bash
npm run check-kode-unik  # Cek kode yang tersedia
```

## 🎯 Kesimpulan

**FUNGSI GENERATE KODE UNIK BERHASIL DIBUAT** dengan fitur:

✅ **Kode unik 1-10** sesuai ketentuan  
✅ **Tidak ada duplikasi** per tanggal  
✅ **Random selection** dari kode yang tersedia  
✅ **Error handling** yang robust  
✅ **Logging detail** untuk monitoring  
✅ **50+ transaksi** berhasil dibuat  
✅ **Harga konsisten** Rp 299.000  
✅ **NodeJS + MySQL** terintegrasi sempurna  

**Semua ketentuan telah dipenuhi!** 🎉
