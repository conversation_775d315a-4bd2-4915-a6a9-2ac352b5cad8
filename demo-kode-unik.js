/**
 * DEMO FUNGSI GENERATE KODE UNIK
 * 
 * File ini mendemonstrasikan cara kerja fungsi generate kode unik
 * sesuai dengan ketentuan yang diminta
 */

const { 
  generateKodeUnik, 
  insertOrder, 
  generateNamaProduk, 
  generateProdukId,
  HARGA_PRODUK,
  STATUS_DEFAULT 
} = require('./main-app');

const db = require('./db');
// DEMO FUNGSI GENERATE KODE UNIK

async function demoGenerateKodeUnik() {
  console.log('🎯 DEMO FUNGSI GENERATE KODE UNIK');
  console.log('═'.repeat(50));
  console.log('Ketentuan:');
  console.log('- Kode unik: 1 sampai 10');
  console.log('- Tidak ada kode unik yang sama per tanggal');
  console.log('- Harga: Rp 299.000 untuk semua produk');
  console.log('');

  try {
    const tanggalDemo = '2025-08-01'; // Tanggal demo
    
    console.log(`📅 Demo untuk tanggal: ${tanggalDemo}`);
    console.log('');

    // Demo generate 5 kode unik untuk tanggal yang sama
    console.log('🔄 Generating 5 kode unik untuk tanggal yang sama:');
    console.log('─'.repeat(40));
    
    for (let i = 1; i <= 5; i++) {
      try {
        console.log(`\n🔢 Percobaan ${i}:`);
        const kodeUnik = await generateKodeUnik(tanggalDemo);
        
        // Buat data order demo
        const orderData = {
          produk_id: generateProdukId(),
          nama_produk: generateNamaProduk(),
          harga: HARGA_PRODUK,
          kode_unik: kodeUnik,
          status: STATUS_DEFAULT,
          tanggal: tanggalDemo
        };
        
        // Insert ke database
        await insertOrder(orderData);
        
        console.log(`   ✅ Berhasil: Kode ${kodeUnik} - ${orderData.nama_produk}`);
        
      } catch (error) {
        console.log(`   ❌ Gagal: ${error.message}`);
        break;
      }
    }

    // Tampilkan data yang tersimpan
    await tampilkanDataDemo(tanggalDemo);
    
    // Demo untuk tanggal berbeda
    console.log('\n🔄 Demo untuk tanggal berbeda:');
    console.log('─'.repeat(40));
    
    const tanggalDemo2 = '2025-08-02';
    console.log(`\n📅 Tanggal baru: ${tanggalDemo2}`);
    
    try {
      const kodeUnik = await generateKodeUnik(tanggalDemo2);
      console.log(`✅ Kode unik untuk tanggal baru: ${kodeUnik}`);
      console.log('   (Kode yang sama bisa digunakan di tanggal berbeda)');
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }

  } catch (error) {
    console.error('❌ Error dalam demo:', error.message);
  } finally {
    db.end();
  }
}

// Fungsi untuk menampilkan data demo
function tampilkanDataDemo(tanggal) {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT id, produk_id, nama_produk, harga, kode_unik, status, tanggal
      FROM orders 
      WHERE tanggal = ?
      ORDER BY kode_unik ASC
    `;

    db.query(sql, [tanggal], (err, results) => {
      if (err) return reject(err);

      console.log(`\n📋 Data tersimpan untuk tanggal ${tanggal}:`);
      console.log('ID\tKode\tProduk ID\tNama Produk\t\tHarga\t\tStatus');
      console.log('─'.repeat(70));
      
      results.forEach(row => {
        const namaProduk = row.nama_produk.length > 15 ? 
          row.nama_produk.substring(0, 15) + '...' : 
          row.nama_produk.padEnd(18);
        
        console.log(`${row.id}\t${row.kode_unik}\t${row.produk_id}\t\t${namaProduk}\tRp ${row.harga.toLocaleString()}\t${row.status}`);
      });
      
      console.log(`\n📊 Total: ${results.length} transaksi`);
      
      // Tampilkan kode unik yang masih tersedia
      const kodeUnikTerpakai = results.map(r => r.kode_unik);
      const kodeUnikTersedia = [];
      for (let i = 1; i <= 10; i++) {
        if (!kodeUnikTerpakai.includes(i)) {
          kodeUnikTersedia.push(i);
        }
      }
      
      console.log(`🔢 Kode unik terpakai: [${kodeUnikTerpakai.join(', ')}]`);
      console.log(`🔢 Kode unik tersedia: [${kodeUnikTersedia.join(', ')}]`);
      
      resolve(results);
    });
  });
}


// JALANKAN DEMO
if (require.main === module) {
  demoGenerateKodeUnik();
}

module.exports = { demoGenerateKodeUnik };
