-- <PERSON><PERSON>t untuk membuat tabel orders
-- Jalankan script ini di MySQL untuk membuat tabel yang diperlukan

CREATE DATABASE IF NOT EXISTS test;
USE test;

-- Hapus tabel jika sudah ada (opsional)
DROP TABLE IF EXISTS orders;

-- Buat tabel orders dengan struktur sesuai permintaan
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    produk_id INT NOT NULL,
    nama_produk VARCHAR(255) NOT NULL,
    harga DECIMAL(10,2) NOT NULL,
    kode_unik INT NOT NULL,
    status VARCHAR(50) NOT NULL,
    tanggal DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Index untuk performa yang lebih baik
    INDEX idx_tanggal (tanggal),
    INDEX idx_kode_unik (kode_unik),
    INDEX idx_status (status),
    
    -- Constraint untuk memastikan kode_unik unik per tanggal
    UNIQUE KEY unique_kode_per_tanggal (tanggal, kode_unik),
    
    -- Constraint untuk memastikan kode_unik hanya 1-10
    CHECK (kode_unik >= 1 AND kode_unik <= 10)
);

-- Tampilkan struktur tabel yang telah dibuat
DESCRIBE orders;

-- Contoh query untuk melihat data
-- SELECT * FROM orders ORDER BY tanggal DESC, kode_unik ASC;
