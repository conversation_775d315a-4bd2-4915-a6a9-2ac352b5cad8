const db = require('./db');

// Konstanta sesuai permintaan
const HARGA = 299000; // 299 ribu untuk semua produk
const STATUS = 'menunggu';

// Fungsi untuk generate nama produk yang bervariasi
function generateNamaProduk() {
  const produkList = [
    'Produk A - Premium',
    'Produk B - Standard', 
    'Produk C - Deluxe',
    'Produk D - Basic',
    'Produk E - Pro',
    'Produk F - Elite',
    'Produk G - Classic',
    'Produk H - Modern'
  ];
  return produkList[Math.floor(Math.random() * produkList.length)];
}

// Fungsi untuk generate tanggal dalam rentang tertentu
function generateTanggalRange(startDaysBack = 10, endDaysBack = 0) {
  const today = new Date();
  const daysBack = Math.floor(Math.random() * (startDaysBack - endDaysBack + 1)) + endDaysBack;
  const randomDate = new Date(today);
  randomDate.setDate(today.getDate() - daysBack);
  return randomDate.toISOString().split('T')[0];
}

// Fungsi untuk generate kode unik yang belum digunakan pada tanggal tertentu
function generateKodeUnik(tanggal) {
  return new Promise((resolve, reject) => {
    const sql = `SELECT kode_unik FROM orders WHERE tanggal = ?`;

    db.query(sql, [tanggal], (err, results) => {
      if (err) {
        console.error('❌ Error saat query kode unik:', err.message);
        return reject(err);
      }

      // Ambil semua kode unik yang sudah digunakan pada tanggal tersebut
      const existing = results.map(r => r.kode_unik);
      
      // Buat array kode unik yang tersedia (1-10)
      const kodeRange = Array.from({ length: 10 }, (_, i) => i + 1);
      
      // Filter kode yang belum digunakan
      const available = kodeRange.filter(k => !existing.includes(k));

      // Jika tidak ada kode yang tersedia
      if (available.length === 0) {
        return reject(new Error(`Kode unik habis pada tanggal ${tanggal}`));
      }

      // Pilih kode secara acak dari yang tersedia
      const randomKode = available[Math.floor(Math.random() * available.length)];
      resolve(randomKode);
    });
  });
}

// Fungsi untuk insert order ke database
function insertOrder(order) {
  return new Promise((resolve, reject) => {
    const sql = `
      INSERT INTO orders (produk_id, nama_produk, harga, kode_unik, status, tanggal)
      VALUES (?, ?, ?, ?, ?, ?)
    `;

    db.query(sql, [
      order.produk_id,
      order.nama_produk,
      order.harga,
      order.kode_unik,
      order.status,
      order.tanggal
    ], (err, result) => {
      if (err) return reject(err);
      resolve(result);
    });
  });
}

// Fungsi utama untuk membuat 50 transaksi
async function generate50Transaksi() {
  const TARGET_TRANSAKSI = 50;
  let berhasil = 0;
  let gagal = 0;
  const tanggalDigunakan = new Map(); // Map untuk tracking transaksi per tanggal
  let attempts = 0;
  const MAX_ATTEMPTS = 100; // Maksimal percobaan untuk menghindari infinite loop

  console.log(`🚀 Target: Membuat ${TARGET_TRANSAKSI} transaksi`);
  console.log('📝 Ketentuan:');
  console.log('   - Harga: Rp 299.000 untuk semua produk');
  console.log('   - Kode unik: 1-10 per tanggal');
  console.log('   - Status: menunggu');
  console.log('   - Menggunakan berbagai tanggal untuk mencapai target');
  console.log('');

  while (berhasil < TARGET_TRANSAKSI && attempts < MAX_ATTEMPTS) {
    attempts++;
    
    try {
      // Generate tanggal random
      const tanggal = generateTanggalRange(30, 0); // 30 hari terakhir
      
      // Cek berapa transaksi yang sudah ada untuk tanggal ini
      const existingCount = await new Promise((resolve, reject) => {
        db.query('SELECT COUNT(*) as count FROM orders WHERE tanggal = ?', [tanggal], (err, results) => {
          if (err) reject(err);
          else resolve(results[0].count);
        });
      });
      
      // Skip jika sudah ada 10 transaksi untuk tanggal ini
      if (existingCount >= 10) {
        continue;
      }
      
      const kode_unik = await generateKodeUnik(tanggal);
      const produk_id = Math.floor(Math.random() * 1000) + 1;
      const nama_produk = generateNamaProduk();

      await insertOrder({
        produk_id,
        nama_produk,
        harga: HARGA,
        kode_unik,
        status: STATUS,
        tanggal,
      });

      berhasil++;
      
      // Update tracking tanggal
      if (!tanggalDigunakan.has(tanggal)) {
        tanggalDigunakan.set(tanggal, 0);
      }
      tanggalDigunakan.set(tanggal, tanggalDigunakan.get(tanggal) + 1);
      
      console.log(`✅ Transaksi ${berhasil}/${TARGET_TRANSAKSI} - Tanggal: ${tanggal}, Produk: ${produk_id}, Kode: ${kode_unik}`);

    } catch (error) {
      gagal++;
      
      if (attempts % 10 === 0) {
        console.log(`⚠️  Percobaan ke-${attempts}, berhasil: ${berhasil}, gagal: ${gagal}`);
      }
    }
  }

  console.log('\n🎉 SELESAI! Ringkasan Akhir:');
  console.log(`   ✅ Berhasil: ${berhasil} transaksi`);
  console.log(`   ❌ Gagal: ${gagal} transaksi`);
  console.log(`   🔄 Total percobaan: ${attempts}`);
  console.log(`   📅 Tanggal yang digunakan: ${tanggalDigunakan.size} hari berbeda`);
  
  console.log('\n📊 Detail per tanggal:');
  const sortedDates = Array.from(tanggalDigunakan.entries()).sort((a, b) => a[0].localeCompare(b[0]));
  sortedDates.forEach(([tanggal, count]) => {
    console.log(`   ${tanggal}: ${count} transaksi`);
  });
  
  // Tampilkan ringkasan total dari database
  await tampilkanStatistikFinal();
  
  process.exit();
}

// Fungsi untuk menampilkan statistik final dari database
function tampilkanStatistikFinal() {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT 
        COUNT(*) as total_transaksi,
        COUNT(DISTINCT tanggal) as total_hari,
        MIN(tanggal) as tanggal_pertama,
        MAX(tanggal) as tanggal_terakhir,
        SUM(harga) as total_nilai
      FROM orders
    `;

    db.query(sql, (err, results) => {
      if (err) return reject(err);

      const stats = results[0];
      
      console.log('\n📈 STATISTIK FINAL DARI DATABASE:');
      console.log('─'.repeat(50));
      console.log(`📊 Total Transaksi: ${stats.total_transaksi}`);
      console.log(`📅 Total Hari Berbeda: ${stats.total_hari}`);
      console.log(`📅 Periode: ${stats.tanggal_pertama} s/d ${stats.tanggal_terakhir}`);
      console.log(`💰 Total Nilai: Rp ${stats.total_nilai.toLocaleString()}`);
      console.log(`💰 Rata-rata per Transaksi: Rp ${(stats.total_nilai / stats.total_transaksi).toLocaleString()}`);
      
      resolve(stats);
    });
  });
}

// Jalankan jika file ini dijalankan langsung
if (require.main === module) {
  generate50Transaksi();
}

module.exports = { generate50Transaksi };
