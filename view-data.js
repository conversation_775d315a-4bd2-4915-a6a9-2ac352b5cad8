// Script untuk melihat data orders yang tersimpan
const db = require('./db');

async function viewAllData() {
  try {
    console.log('📊 Mengambil semua data orders...\n');
    
    const sql = `
      SELECT 
        id,
        produk_id,
        nama_produk,
        harga,
        kode_unik,
        status,
        tanggal,
        created_at
      FROM orders 
      ORDER BY tanggal DESC, kode_unik ASC
    `;
    
    const results = await new Promise((resolve, reject) => {
      db.query(sql, (err, results) => {
        if (err) reject(err);
        else resolve(results);
      });
    });
    
    if (results.length === 0) {
      console.log('📭 Tidak ada data orders. Jalankan: npm start');
      return;
    }
    
    // Tampilkan header
    console.log('ID\tProduk ID\tNama Produk\t\t\tHarga\t\tKode Unik\tStatus\t\tTanggal\t\tCreated At');
    console.log('─'.repeat(120));
    
    // Tampilkan data
    results.forEach(row => {
      const namaProduk = row.nama_produk.length > 20 ? 
        row.nama_produk.substring(0, 20) + '...' : 
        row.nama_produk.padEnd(23);
      
      const harga = `Rp ${row.harga.toLocaleString()}`.padEnd(12);
      const createdAt = new Date(row.created_at).toLocaleString('id-ID');
      
      console.log(`${row.id}\t${row.produk_id}\t\t${namaProduk}\t${harga}\t${row.kode_unik}\t\t${row.status}\t\t${row.tanggal}\t${createdAt}`);
    });
    
    // Statistik
    console.log('\n📈 Statistik:');
    console.log(`   Total transaksi: ${results.length}`);
    
    // Group by tanggal
    const groupByDate = results.reduce((acc, row) => {
      const date = row.tanggal;
      if (!acc[date]) acc[date] = 0;
      acc[date]++;
      return acc;
    }, {});
    
    console.log('   Transaksi per tanggal:');
    Object.entries(groupByDate).forEach(([date, count]) => {
      console.log(`     ${date}: ${count} transaksi`);
    });
    
    // Group by kode unik
    const groupByKode = results.reduce((acc, row) => {
      const kode = row.kode_unik;
      if (!acc[kode]) acc[kode] = 0;
      acc[kode]++;
      return acc;
    }, {});
    
    console.log('   Penggunaan kode unik:');
    for (let i = 1; i <= 10; i++) {
      const count = groupByKode[i] || 0;
      console.log(`     Kode ${i}: ${count} kali digunakan`);
    }
    
  } catch (error) {
    console.error('❌ Error saat mengambil data:', error.message);
  } finally {
    db.end();
  }
}

async function viewDataByDate(tanggal) {
  try {
    console.log(`📊 Data orders untuk tanggal: ${tanggal}\n`);
    
    const sql = `
      SELECT * FROM orders 
      WHERE tanggal = ?
      ORDER BY kode_unik ASC
    `;
    
    const results = await new Promise((resolve, reject) => {
      db.query(sql, [tanggal], (err, results) => {
        if (err) reject(err);
        else resolve(results);
      });
    });
    
    if (results.length === 0) {
      console.log(`📭 Tidak ada data untuk tanggal ${tanggal}`);
      return;
    }
    
    console.log('Kode Unik\tProduk ID\tNama Produk\t\t\tHarga\t\tStatus');
    console.log('─'.repeat(80));
    
    results.forEach(row => {
      const namaProduk = row.nama_produk.length > 20 ? 
        row.nama_produk.substring(0, 20) + '...' : 
        row.nama_produk.padEnd(23);
      
      console.log(`${row.kode_unik}\t\t${row.produk_id}\t\t${namaProduk}\tRp ${row.harga.toLocaleString()}\t${row.status}`);
    });
    
    console.log(`\n📊 Total: ${results.length} transaksi pada ${tanggal}`);
    
    // Tampilkan kode unik yang tersedia
    const usedCodes = results.map(r => r.kode_unik);
    const availableCodes = Array.from({length: 10}, (_, i) => i + 1).filter(k => !usedCodes.includes(k));
    
    if (availableCodes.length > 0) {
      console.log(`🔢 Kode unik yang masih tersedia: ${availableCodes.join(', ')}`);
    } else {
      console.log('⚠️  Semua kode unik (1-10) sudah terpakai untuk tanggal ini');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    db.end();
  }
}

// Jalankan berdasarkan argument
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length > 0) {
    // Jika ada argument tanggal, tampilkan data untuk tanggal tersebut
    const tanggal = args[0];
    viewDataByDate(tanggal);
  } else {
    // Jika tidak ada argument, tampilkan semua data
    viewAllData();
  }
}

module.exports = { viewAllData, viewDataByDate };
