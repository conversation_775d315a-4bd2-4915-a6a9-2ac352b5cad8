const mysql = require('mysql2');

// Konfigurasi database
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '',     // sesuaikan dengan password MySQL Anda
  database: 'test', // nama database
  charset: 'utf8mb4',
  timezone: '+00:00',
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true
};

// Buat koneksi dengan error handling yang lebih baik
const conn = mysql.createConnection(dbConfig);

// Event handler untuk koneksi
conn.connect((err) => {
  if (err) {
    console.error('❌ Gagal koneksi ke database:', err.message);

    // Berikan tips berdasarkan jenis error
    if (err.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('💡 Tips: Periksa username dan password MySQL');
    } else if (err.code === 'ECONNREFUSED') {
      console.log('💡 Tips: Pastikan MySQL server sudah berjalan');
    } else if (err.code === 'ER_BAD_DB_ERROR') {
      console.log('💡 Tips: Database "test" belum ada. Jalankan: npm run setup');
    }

    process.exit(1);
  }

  console.log('✅ Koneksi ke database berhasil!');
  console.log(`📊 Database: ${dbConfig.database}`);
});

// Handle error saat koneksi terputus
conn.on('error', (err) => {
  console.error('❌ Database connection error:', err);

  if (err.code === 'PROTOCOL_CONNECTION_LOST') {
    console.log('🔄 Mencoba reconnect...');
    // Bisa ditambahkan logic reconnect di sini jika diperlukan
  } else {
    throw err;
  }
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🔄 Menutup koneksi database...');
  conn.end(() => {
    console.log('✅ Koneksi database ditutup');
    process.exit(0);
  });
});

module.exports = conn;
