/**
 * EXPRESS SERVER UNTUK UI ORDER MANAGEMENT
 * 
 * Server ini menyediakan:
 * 1. Static files untuk UI
 * 2. REST API untuk operasi database
 * 3. Integration dengan fungsi-fungsi yang sudah ada
 */

const express = require('express');
const path = require('path');
const cors = require('cors');
const db = require('./db');

// Import fungsi-fungsi yang sudah ada
const { 
  generateKodeUnik, 
  insertOrder, 
  generateNamaProduk, 
  generateProdukId,
  generateTanggal,
  tampilkanStatistikDatabase,
  HARGA_PRODUK,
  STATUS_DEFAULT 
} = require('./main-app');

const { 
  cekKodeUnikTersediaPerTanggal,
  cekKodeUnikTersediaGlobal 
} = require('./kode-unik-functions');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// ========================================
// API ROUTES
// ========================================

// GET /api/orders - Ambil semua data orders
app.get('/api/orders', (req, res) => {
  const sql = `
    SELECT 
      id, produk_id, nama_produk, harga, kode_unik, status, tanggal, created_at
    FROM orders 
    ORDER BY created_at DESC
  `;
  
  db.query(sql, (err, results) => {
    if (err) {
      console.error('Error fetching orders:', err);
      return res.status(500).json({
        success: false,
        message: 'Gagal mengambil data orders',
        error: err.message
      });
    }
    
    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// GET /api/stats - Ambil statistik
app.get('/api/stats', (req, res) => {
  const sql = `
    SELECT 
      COUNT(*) as total_transaksi,
      COUNT(DISTINCT tanggal) as total_hari,
      COUNT(DISTINCT kode_unik) as total_kode_unik,
      SUM(harga) as total_nilai,
      AVG(harga) as rata_rata_harga,
      MIN(tanggal) as tanggal_pertama,
      MAX(tanggal) as tanggal_terakhir
    FROM orders
  `;
  
  db.query(sql, (err, results) => {
    if (err) {
      console.error('Error fetching stats:', err);
      return res.status(500).json({
        success: false,
        message: 'Gagal mengambil statistik',
        error: err.message
      });
    }
    
    res.json({
      success: true,
      data: results[0]
    });
  });
});

// POST /api/generate-transaksi - Generate transaksi
app.post('/api/generate-transaksi', async (req, res) => {
  const TARGET_MINIMAL = 50;
  let jumlahBerhasil = 0;
  let jumlahGagal = 0;
  let percobaan = 0;
  const MAX_PERCOBAAN = 200;
  const tanggalDigunakan = new Set();

  try {
    console.log('🚀 Memulai generate transaksi via API...');
    
    while (jumlahBerhasil < TARGET_MINIMAL && percobaan < MAX_PERCOBAAN) {
      percobaan++;
      
      try {
        const tanggal = generateTanggal(60);
        const produkId = generateProdukId();
        const namaProduk = generateNamaProduk();
        const kodeUnik = await generateKodeUnik(tanggal);
        
        const orderData = {
          produk_id: produkId,
          nama_produk: namaProduk,
          harga: HARGA_PRODUK,
          kode_unik: kodeUnik,
          status: STATUS_DEFAULT,
          tanggal: tanggal
        };

        await insertOrder(orderData);
        
        jumlahBerhasil++;
        tanggalDigunakan.add(tanggal);
        
        console.log(`✅ Transaksi ${jumlahBerhasil}/${TARGET_MINIMAL} - ${namaProduk}`);
        
      } catch (error) {
        jumlahGagal++;
        
        if (error.message.includes('Tidak ada kode unik yang tersedia')) {
          continue;
        } else {
          console.error(`❌ Error tidak terduga:`, error.message);
        }
      }
    }

    res.json({
      success: true,
      message: `Berhasil generate ${jumlahBerhasil} transaksi`,
      data: {
        berhasil: jumlahBerhasil,
        gagal: jumlahGagal,
        percobaan: percobaan,
        tanggal_digunakan: tanggalDigunakan.size,
        target_tercapai: jumlahBerhasil >= TARGET_MINIMAL
      }
    });
    
  } catch (error) {
    console.error('Error in generate-transaksi:', error);
    res.status(500).json({
      success: false,
      message: 'Terjadi kesalahan saat generate transaksi',
      error: error.message
    });
  }
});

// DELETE /api/reset - Reset semua data
app.delete('/api/reset', (req, res) => {
  db.query('DELETE FROM orders', (err, result) => {
    if (err) {
      console.error('Error resetting data:', err);
      return res.status(500).json({
        success: false,
        message: 'Gagal reset data',
        error: err.message
      });
    }
    
    // Reset auto increment
    db.query('ALTER TABLE orders AUTO_INCREMENT = 1', (err2) => {
      if (err2) {
        console.error('Error resetting auto increment:', err2);
      }
      
      res.json({
        success: true,
        message: 'Data berhasil direset',
        data: {
          deleted_count: result.affectedRows
        }
      });
    });
  });
});

// GET /api/kode-unik-status - Cek status kode unik
app.get('/api/kode-unik-status', async (req, res) => {
  try {
    const today = new Date().toISOString().split('T')[0];
    
    const globalStatus = await cekKodeUnikTersediaGlobal();
    const todayStatus = await cekKodeUnikTersediaPerTanggal(today);
    
    res.json({
      success: true,
      data: {
        global: globalStatus,
        today: todayStatus
      }
    });
    
  } catch (error) {
    console.error('Error checking kode unik status:', error);
    res.status(500).json({
      success: false,
      message: 'Gagal cek status kode unik',
      error: error.message
    });
  }
});

// GET /api/orders/:date - Ambil data berdasarkan tanggal
app.get('/api/orders/:date', (req, res) => {
  const { date } = req.params;
  
  const sql = `
    SELECT 
      id, produk_id, nama_produk, harga, kode_unik, status, tanggal, created_at
    FROM orders 
    WHERE tanggal = ?
    ORDER BY kode_unik ASC
  `;
  
  db.query(sql, [date], (err, results) => {
    if (err) {
      console.error('Error fetching orders by date:', err);
      return res.status(500).json({
        success: false,
        message: 'Gagal mengambil data orders',
        error: err.message
      });
    }
    
    res.json({
      success: true,
      data: results,
      count: results.length,
      date: date
    });
  });
});

// POST /api/orders - Tambah order manual
app.post('/api/orders', async (req, res) => {
  try {
    const { nama_produk, tanggal } = req.body;
    
    if (!nama_produk || !tanggal) {
      return res.status(400).json({
        success: false,
        message: 'Nama produk dan tanggal harus diisi'
      });
    }
    
    const produkId = generateProdukId();
    const kodeUnik = await generateKodeUnik(tanggal);
    
    const orderData = {
      produk_id: produkId,
      nama_produk: nama_produk,
      harga: HARGA_PRODUK,
      kode_unik: kodeUnik,
      status: STATUS_DEFAULT,
      tanggal: tanggal
    };

    const result = await insertOrder(orderData);
    
    res.json({
      success: true,
      message: 'Order berhasil ditambahkan',
      data: {
        id: result.insertId,
        ...orderData
      }
    });
    
  } catch (error) {
    console.error('Error adding order:', error);
    res.status(500).json({
      success: false,
      message: 'Gagal menambah order',
      error: error.message
    });
  }
});

// ========================================
// STATIC ROUTES
// ========================================

// Serve main page
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint tidak ditemukan'
  });
});

// Error handler
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json({
    success: false,
    message: 'Terjadi kesalahan server',
    error: err.message
  });
});

// ========================================
// START SERVER
// ========================================

app.listen(PORT, () => {
  console.log('🚀 SERVER STARTED');
  console.log('═'.repeat(50));
  console.log(`📊 Order Management UI: http://localhost:${PORT}`);
  console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
  console.log('');
  console.log('📋 Available Endpoints:');
  console.log('   GET  /                     - Main UI');
  console.log('   GET  /api/orders           - Get all orders');
  console.log('   GET  /api/orders/:date     - Get orders by date');
  console.log('   GET  /api/stats            - Get statistics');
  console.log('   GET  /api/kode-unik-status - Check kode unik status');
  console.log('   POST /api/generate-transaksi - Generate 50 transactions');
  console.log('   POST /api/orders           - Add manual order');
  console.log('   DELETE /api/reset          - Reset all data');
  console.log('');
  console.log('🎯 Ready to serve requests!');
});

module.exports = app;
